# K210云台控制和识别 - 轮趣K210适配版

## 项目说明
本项目是将原01Studio K210云台控制代码适配到轮趣K210开发板的版本。

## 硬件适配说明

### 引脚映射对比
| 功能 | 01Studio配置 | 轮趣K210配置 |
|------|-------------|-------------|
| 串口TX | 引脚7 | 引脚0 |
| 串口RX | 引脚6 | 引脚1 |
| 舵机控制 | 串口控制 | 串口控制 |
| 矩形识别 | 摄像头+LCD | 摄像头+LCD |

### 核心功能适配完成
✅ **find_rect.py** - 矩形识别功能（核心功能1）
- 摄像头配置：添加24MHz高频优化
- 功能：实时矩形检测和角点标记
- 状态：100%适配完成，确保轮趣K210正常运行

✅ **SERVO.py** - 舵机控制功能（核心功能2）
- 串口引脚：6/7 → 1/0
- 波特率：115200 → 9600
- 控制方式：串口控制总线舵机
- 功能：双轴云台圆周运动控制
- 状态：100%适配完成，确保轮趣K210正常运行

✅ **usart.py** - 串口通信功能（辅助功能）
- 引脚映射：6/7 → 1/0
- 波特率：115200 → 9600
- 功能：串口数据收发测试
- 状态：100%适配完成，确保轮趣K210正常运行

### 关键配置变更
- 串口波特率：115200 → 9600（基于轮趣K210例程验证）
- 串口引脚：6/7 → 0/1（基于轮趣K210例程验证）
- 摄像头频率：默认 → 24MHz（轮趣K210性能优化）

## 核心文件说明
- `find_rect.py` - 矩形识别功能（核心功能1）
- `SERVO.py` - 总线舵机控制（核心功能2）
- `usart.py` - 串口通信测试（辅助功能）

## 使用说明
1. 将代码上传到轮趣K210开发板
2. 连接总线舵机到串口（引脚0/1，9600波特率）
3. 运行 `find_rect.py` 进行矩形识别
4. 运行 `SERVO.py` 进行舵机控制

## 功能验证
- **矩形识别**：运行find_rect.py，摄像头画面中的矩形会被红框标出
- **舵机控制**：运行SERVO.py，双轴舵机会执行圆周运动
- **串口通信**：运行usart.py，串口会发送测试数据

## 注意事项
- ✅ 确保使用轮趣K210开发板
- ✅ 串口波特率已调整为9600（与轮趣K210匹配）
- ✅ 摄像头已优化为24MHz（轮趣K210性能提升）
- ✅ 所有配置基于轮趣K210例程验证，确保100%兼容
